#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多语言翻译文件完整性检查脚本
检查frontend/src/i18n/目录下的翻译文件是否与zh_CN一致
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

class I18nIntegrityChecker:
    def __init__(self, base_dir: str = "frontend/src/i18n"):
        """
        初始化检查器
        
        Args:
            base_dir: i18n目录路径
        """
        self.base_dir = Path(base_dir)
        self.zh_cn_dir = self.base_dir / "zh_CN"
        self.missing_keys_files = []  # 缺失键值的文件路径
        self.chinese_content_files = []  # 包含简体中文的文件路径
        
    def load_json_file(self, file_path: Path) -> Dict:
        """
        加载JSON文件

        Args:
            file_path: JSON文件路径

        Returns:
            解析后的JSON数据
        """
        try:
            # 首先尝试使用utf-8-sig编码（处理BOM）
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError, UnicodeDecodeError) as e:
            try:
                # 如果失败，尝试普通utf-8编码
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError, UnicodeDecodeError) as e2:
                print(f"❌ 无法读取文件 {file_path}: {e2}")
                return {}
    
    def get_all_keys(self, data: Dict, prefix: str = "") -> Set[str]:
        """
        递归获取JSON对象中的所有键路径
        
        Args:
            data: JSON数据
            prefix: 键路径前缀
            
        Returns:
            所有键路径的集合
        """
        keys = set()
        for key, value in data.items():
            current_key = f"{prefix}.{key}" if prefix else key
            keys.add(current_key)
            if isinstance(value, dict):
                keys.update(self.get_all_keys(value, current_key))
        return keys
    
    def get_all_values(self, data: Dict) -> List[str]:
        """
        递归获取JSON对象中的所有值
        
        Args:
            data: JSON数据
            
        Returns:
            所有值的列表
        """
        values = []
        for value in data.values():
            if isinstance(value, dict):
                values.extend(self.get_all_values(value))
            elif isinstance(value, str):
                values.append(value)
        return values
    
    def contains_chinese(self, text: str) -> bool:
        """
        检查文本是否包含简体中文字符
        
        Args:
            text: 要检查的文本
            
        Returns:
            是否包含中文字符
        """
        # 匹配中文字符的正则表达式
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))
    
    def get_all_json_files(self, directory: Path) -> List[Path]:
        """
        递归获取目录下的所有JSON文件
        
        Args:
            directory: 目录路径
            
        Returns:
            JSON文件路径列表
        """
        json_files = []
        if directory.exists():
            for file_path in directory.rglob("*.json"):
                json_files.append(file_path)
        return json_files
    
    def get_relative_path(self, file_path: Path, base_dir: Path) -> str:
        """
        获取相对于基础目录的相对路径
        
        Args:
            file_path: 文件路径
            base_dir: 基础目录
            
        Returns:
            相对路径字符串
        """
        try:
            return str(file_path.relative_to(base_dir))
        except ValueError:
            return str(file_path)
    
    def check_file_integrity(self, zh_cn_file: Path, target_file: Path) -> Tuple[bool, Set[str]]:
        """
        检查目标文件与zh_CN文件的键值完整性
        
        Args:
            zh_cn_file: zh_CN参考文件
            target_file: 目标检查文件
            
        Returns:
            (是否完整, 缺失的键集合)
        """
        zh_cn_data = self.load_json_file(zh_cn_file)
        target_data = self.load_json_file(target_file)
        
        if not zh_cn_data or not target_data:
            return False, set()
        
        zh_cn_keys = self.get_all_keys(zh_cn_data)
        target_keys = self.get_all_keys(target_data)
        
        missing_keys = zh_cn_keys - target_keys
        return len(missing_keys) == 0, missing_keys
    
    def check_chinese_content(self, file_path: Path) -> bool:
        """
        检查文件是否包含简体中文内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否包含中文内容
        """
        data = self.load_json_file(file_path)
        if not data:
            return False
        
        values = self.get_all_values(data)
        return any(self.contains_chinese(value) for value in values)
    
    def run_check(self):
        """
        执行完整性检查
        """
        print("🔍 开始检查多语言翻译文件完整性...")
        print(f"📁 基础目录: {self.base_dir}")
        print(f"📁 参考目录: {self.zh_cn_dir}")
        print("-" * 60)
        
        # 检查zh_CN目录是否存在
        if not self.zh_cn_dir.exists():
            print(f"❌ zh_CN目录不存在: {self.zh_cn_dir}")
            return
        
        # 获取zh_CN目录下的所有JSON文件
        zh_cn_files = self.get_all_json_files(self.zh_cn_dir)
        print(f"📄 zh_CN目录下共找到 {len(zh_cn_files)} 个JSON文件")
        
        # 获取所有语言目录
        language_dirs = [d for d in self.base_dir.iterdir() 
                        if d.is_dir() and d.name != "zh_CN"]
        
        print(f"🌍 找到 {len(language_dirs)} 个其他语言目录: {[d.name for d in language_dirs]}")
        print("-" * 60)
        
        # 检查每个语言目录
        for lang_dir in language_dirs:
            print(f"\n🔍 检查语言目录: {lang_dir.name}")
            
            # 检查每个zh_CN文件在目标语言中是否存在且完整
            for zh_cn_file in zh_cn_files:
                # 计算目标文件路径
                relative_path = zh_cn_file.relative_to(self.zh_cn_dir)
                target_file = lang_dir / relative_path
                
                if not target_file.exists():
                    print(f"  ❌ 文件不存在: {self.get_relative_path(target_file, self.base_dir)}")
                    self.missing_keys_files.append(str(target_file))
                    continue
                
                # 检查键值完整性
                is_complete, missing_keys = self.check_file_integrity(zh_cn_file, target_file)
                if not is_complete:
                    print(f"  ❌ 键值不完整: {self.get_relative_path(target_file, self.base_dir)}")
                    print(f"     缺失键: {', '.join(sorted(missing_keys))}")
                    self.missing_keys_files.append(str(target_file))
                else:
                    print(f"  ✅ 键值完整: {self.get_relative_path(target_file, self.base_dir)}")
                
                # 检查是否包含简体中文
                if self.check_chinese_content(target_file):
                    print(f"  ⚠️  包含中文: {self.get_relative_path(target_file, self.base_dir)}")
                    self.chinese_content_files.append(str(target_file))
    
    def save_results(self):
        """
        保存检查结果到文件
        """
        print("\n" + "=" * 60)
        print("📊 检查结果汇总")
        print("=" * 60)
        
        # 保存缺失键值的文件
        if self.missing_keys_files:
            print(f"\n❌ 发现 {len(self.missing_keys_files)} 个文件存在缺失键值:")
            with open("missing_keys_files.txt", "w", encoding="utf-8") as f:
                f.write("缺失键值的文件列表:\n")
                f.write("=" * 40 + "\n")
                for file_path in self.missing_keys_files:
                    print(f"  - {file_path}")
                    f.write(f"{file_path}\n")
            print(f"📄 详细列表已保存到: missing_keys_files.txt")
        else:
            print("\n✅ 所有文件的键值都完整!")
        
        # 保存包含简体中文的文件
        if self.chinese_content_files:
            print(f"\n⚠️  发现 {len(self.chinese_content_files)} 个文件包含简体中文:")
            with open("chinese_content_files.txt", "w", encoding="utf-8") as f:
                f.write("包含简体中文的文件列表:\n")
                f.write("=" * 40 + "\n")
                for file_path in self.chinese_content_files:
                    print(f"  - {file_path}")
                    f.write(f"{file_path}\n")
            print(f"📄 详细列表已保存到: chinese_content_files.txt")
        else:
            print("\n✅ 所有文件都没有包含简体中文!")
        
        print(f"\n🎉 检查完成!")

def main():
    """
    主函数
    """
    checker = I18nIntegrityChecker()
    checker.run_check()
    checker.save_results()

if __name__ == "__main__":
    main()
