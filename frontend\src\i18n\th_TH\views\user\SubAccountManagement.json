{"SubAccountManagement": {"accountAssets": "账户资产", "addApiKey": "API", "agreeTerms": "我同意", "agreeTermsError": "API", "all": "ทั้งหมด", "apiTerms": "API", "apiTermsContent": "API，API。", "apikey": "API Key", "apikeyAdded": "API", "apikeyUpdated": "API", "cancel": "ยกเลิก", "completionTime": "完成时间", "editApiKey": "API", "error": "ข้อผิดพลาด", "getSubAccountError": "获取子账号详情失败", "loading": "กำลังโหลด...", "noData": "暂无子账号数据", "operation": "การดำเนินการ", "parentAccount": "母账号", "profitDetails": "รายละเอียดกำไร", "save": "保存", "saveApiKeyError": "API", "secretkey": "Secret Key", "selectAccount": "选择账号", "statistics": "子账号统计", "status": "สถานะ", "status_disabled": "已停用", "status_enabled": "已启用", "status_expired": "已失效", "status_inactive": "未启用", "subAccount": "子账号", "subAccountCount": "子账号数量", "subtitle": "管理您的子账号", "success": "สำเร็จ", "taskStatus_completed": "已完成", "taskStatus_notRunning": "未运行", "taskStatus_running": "执行中", "task_status": "สถานะ", "title": "การจัดการบัญชีย่อย", "totalAssets": "总资产", "totalProfit": "总收益", "uncollectedFunds": "未归集资金", "apikeyError": "API Key", "secretkeyError": "Secret Key"}}