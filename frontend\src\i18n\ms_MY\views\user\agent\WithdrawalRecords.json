{"WithdrawalRecords": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON> rekod pengel<PERSON>ran anda", "noData": "Tiada rekod pen<PERSON>", "network": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "fee": "<PERSON><PERSON>", "amountReceived": "<PERSON><PERSON><PERSON>", "originalBalance": "<PERSON><PERSON>", "remainingBalance": "<PERSON><PERSON>", "status": "Status", "createdAt": "<PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "processing": "Sedang Diproses", "completed": "Se<PERSON><PERSON>", "failed": "Gaga<PERSON>", "applyWithdrawal": "<PERSON><PERSON>", "applySuccess": "<PERSON><PERSON><PERSON><PERSON>", "applyFailed": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "view": "Lihat", "confirmWithdrawal": "<PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON>", "operation": "Operasi", "confirmSuccess": "<PERSON><PERSON><PERSON>", "confirmFailed": "<PERSON><PERSON><PERSON>l", "withdrawalCheckNotice": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>, sila tunggu pengesahan", "currentWalletBalance": "Baki Dom<PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "addressRequired": "<PERSON><PERSON><PERSON> pen<PERSON>ran tidak boleh kosong", "amountInvalid": "Jumlah minimum pengeluaran ialah 1000", "amountExceedsBalance": "<PERSON><PERSON><PERSON> pen<PERSON> tidak boleh mele<PERSON>hi baki semasa", "withdrawalSubmitted": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON> ber<PERSON>a", "withdrawalError": "<PERSON><PERSON><PERSON><PERSON> pengeluaran gagal", "cancelSuccess": "Pembatalan berjaya", "cancelFailed": "Pembatalan gagal", "fetchRecordsFailed": "Gagal mendapatkan rekod pengel<PERSON>ran", "fetchBalanceFailed": "<PERSON><PERSON> men<PERSON><PERSON><PERSON> baki"}}