{"AccountManagement": {"account": "<PERSON><PERSON><PERSON>", "accountAdded": "<PERSON><PERSON>un telah ditambah", "accountManagement": "<PERSON><PERSON><PERSON><PERSON>", "accountRemark": "Catatan Akaun <PERSON>", "accountRemarkHint": "Digunakan untuk membezakan akaun dagangan yang berbeza", "accountUpdated": "<PERSON><PERSON><PERSON><PERSON> akaun telah dike<PERSON>", "addAccount": "Tambah Akaun <PERSON>", "addTime": "<PERSON><PERSON>", "agreeTerms": "<PERSON>a telah membaca dan berse<PERSON>ju", "agreeTermsError": "<PERSON>la baca dan setuju terma penggunaan kunci <PERSON>", "apiTerms": "Terma Penggunaan Kunci API", "cancel": "<PERSON><PERSON>", "completedCount": "Bilangan Selesai", "edit": "Edit", "editAccount": "<PERSON> <PERSON><PERSON><PERSON>", "noAccountData": "Tiada data akaun induk", "operation": "Operasi", "profitDetails": "<PERSON><PERSON><PERSON>", "runningCount": "Bilangan Be<PERSON>lan", "save": "Simpan", "saveAccountError": "<PERSON><PERSON>un", "secretkey": "Kunci API", "status": "Status", "statusDisabled": "Din<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusEnabled": "Diaktifkan", "statusExpired": "Tamat Tempoh", "statusInactive": "Tidak Aktif", "subAccountCount": "Bilangan Akaun Sub", "subtitle": "<PERSON><PERSON> akaun induk dagangan anda", "taskStatusCompleted": "Se<PERSON><PERSON>", "taskStatusNotRunning": "Tidak <PERSON>", "taskStatusRunning": "Sedang Be<PERSON>lan", "task_status": "Status Tugas", "totalAssets": "<PERSON><PERSON><PERSON>", "totalProfit": "<PERSON><PERSON><PERSON>", "todayProfit": "<PERSON><PERSON><PERSON><PERSON>", "uncollectedFunds": "Dana <PERSON>", "statistics": "Statistik Akaun Induk", "accountCount": "Bilangan <PERSON>", "totalAccountCount": "<PERSON><PERSON><PERSON>", "accountRequired": "<PERSON><PERSON> masukkan akaun induk", "apikeyRequired": "Sila masukkan API KEY", "secretkeyRequired": "<PERSON><PERSON> masukkan kunci <PERSON>"}}