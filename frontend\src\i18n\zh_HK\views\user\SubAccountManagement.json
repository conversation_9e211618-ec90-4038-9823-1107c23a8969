{"SubAccountManagement": {"accountAssets": "帳戶資產", "addApiKey": "新增API金鑰", "agreeTerms": "我同意", "agreeTermsError": "请同意API使用条款", "all": "全部", "apiTerms": "API使用条款", "apiTermsContent": "使用API金鑰需遵守相关条款和条件，确保API金鑰的安全性。", "apikey": "API Key", "apikeyAdded": "API金鑰已新增", "apikeyUpdated": "API金鑰已更新", "cancel": "取消", "completionTime": "完成時間", "editApiKey": "编辑API金鑰", "error": "錯誤", "getSubAccountError": "获取子帳號详情失败", "loading": "載入中...", "noData": "暂无子帳號資料", "operation": "操作", "parentAccount": "母帳號", "profitDetails": "盈利明細", "save": "保存", "saveApiKeyError": "保存API金鑰失败", "secretkey": "Secret Key", "selectAccount": "选择帳號", "statistics": "子帳號统计", "status": "狀態", "status_disabled": "已停用", "status_enabled": "已啟用", "status_expired": "已失效", "status_inactive": "未啟用", "subAccount": "子帳號", "subAccountCount": "子帳號數量", "subtitle": "管理您的子帳號", "success": "成功", "taskStatus_completed": "已完成", "taskStatus_notRunning": "未运行", "taskStatus_running": "执行中", "task_status": "任務狀態", "title": "子帳戶管理", "totalAssets": "总資產", "totalProfit": "总收益", "uncollectedFunds": "未歸集資金", "apikeyError": "请输入API Key", "secretkeyError": "请输入Secret Key"}}